import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  main: {
    width: '100%',
    flex: 1,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imgTitleContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12.7,
    marginBottom: 40,
  },
  logo: {
    width: 129.6,
    height: 90,
  },
  title: {
    fontSize: 17,
    lineHeight: 22,
    color: '#2054A5',
    textAlign: 'center',
    width: 175,
  },
  buttonsContainer: {
    position: 'absolute',
    bottom: 48,
    width: '100%',
    paddingHorizontal: 16,
    gap: 19,
    alignItems: 'center',
  },
  biometricButton: {
    width: '80%',
  },
  passwordLink: {
    textAlign: 'center',
    color: '#2054A5',
    fontSize: 17,
    textDecorationLine: 'underline',
  },
  button: {
    width: '80%',
  },
  drawer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 8,
    width: '90%',
    alignSelf: 'center',
  },
  drawerTitle: {
    fontSize: 32,
    fontFamily: 'Poppins-Bold',
    color: '#2054A5',
    marginBottom: 20,
  },
  loginForm: {
    gap: 34,
  },
  formGroup: {
    gap: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 10,
  },
  forgotPassword: {
    textAlign: 'center',
    color: '#749CFF',
  },
});
