
import React, { useState } from "react";
import useToastStore from "../../stores/toast";
import { forgotPassword } from "../../services/reset_password.service";


interface ForgottenPasswordProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

const ForgottenPassword: React.FC<ForgottenPasswordProps> = ({ open, setOpen }) => {
  const { show } = useToastStore();
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState("");

  const handleForgotPassword = async () => {
    if (!email || !email.trim()) {
      show({
        msg: "Por favor, ingresa un correo electrónico válido",
        type: "error"
      });
      return;
    }

    setLoading(true);

    try {
      await forgotPassword(email);
      show({
        msg: "Se ha enviado un enlace de restablecimiento de contraseña a tu correo electrónico.",
        type: "success",
      });
      setEmail("");
      setOpen(false);
    } catch (error: any) {
      show({
        msg: error.message || "Error al enviar el correo de recuperación",
        type: "error"
      });
      console.error("Error en recuperación de contraseña:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
  <></>
  );
};



export default ForgottenPassword;
