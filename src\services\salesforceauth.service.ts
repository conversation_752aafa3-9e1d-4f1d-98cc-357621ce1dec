import { REST } from "@env";

const CONTROLLER = "/salesforceAuth";

export const getAccessToken = async (cedula:string) => {
  const ENDPOINT = `${REST}${CONTROLLER}`;
  const response = await fetch(`${ENDPOINT}/getAccessToken`, {
    method: "POST",
    body: JSON.stringify({ cedula }),
  });
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.message);
  }
  return data;
};
