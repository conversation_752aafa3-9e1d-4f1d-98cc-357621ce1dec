import { REST } from "@env";

const CONTROLLER = "/usuarios";

// Types
interface UserResponse {
  // Adjust this according to your actual API response structure
  ok: boolean;
  message?: string;
  error?: string;
  [key: string]: any;
}

// Functions

export const checkEmail = async (email: string): Promise<UserResponse> => {
  const ENDPOINT = `${REST}${CONTROLLER}`;
  const response = await fetch(`${ENDPOINT}/getUserByEmail?email=${email}`);
  const data: UserResponse = await response.json();
  if (!response.ok) {
    throw new Error(data.message || "Failed to fetch user by email");
  }
  return data;
};

export const checkNumDoc = async (numDoc: string): Promise<UserResponse> => {
  const ENDPOINT = `${REST}${CONTROLLER}`;
  const response = await fetch(`${ENDPOINT}/getUserByNif?nif=${numDoc}`);
  const data: UserResponse = await response.json();
  if (!response.ok) {
    throw new Error(data.message || "Failed to fetch user by NIF");
  }
  return data;
};

export const createUser = async (formData: FormData): Promise<UserResponse> => {
  const ENDPOINT = `${REST}${CONTROLLER}/create`;
  const response = await fetch(ENDPOINT, {
    method: "POST",
    body: formData,
  });
  const data: UserResponse = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Failed to create user");
  }
  return data;
};

export const login = async (
  email: string,
  password: string
): Promise<UserResponse> => {
  const ENDPOINT = `${REST}${CONTROLLER}/login`;
  const response = await fetch(ENDPOINT, {
    method: "POST",
    body: JSON.stringify({ email, password }),
    headers: {
      "Content-Type": "application/json",
    },
  });
  const data: UserResponse = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Login failed");
  }
  return data;
};

export const getPersonalInformation = async (
  idUsuario: number | string,
  token: string
): Promise<UserResponse> => {
  const ENDPOINT = `${REST}${CONTROLLER}/getPersonalInformation?id=${idUsuario}`;
  const response = await fetch(ENDPOINT, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  const data: UserResponse = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Failed to fetch personal information");
  }
  return data;
};

export const updatePersonalInformation = async (
  token: string,
  payload: Record<string, any>
): Promise<UserResponse> => {
  const ENDPOINT = `${REST}${CONTROLLER}/updatePersonalInformation`;
  const response = await fetch(ENDPOINT, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  });
  const data: UserResponse = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Failed to update personal information");
  }
  return data;
};

export const resetToken = async (
  idUsuario: string | number,
  token: string
): Promise<UserResponse> => {
  const formData = new FormData();
  formData.append("idUsuario", idUsuario.toString());
  const ENDPOINT = `${REST}${CONTROLLER}/resetToken`;
  const response = await fetch(ENDPOINT, {
    method: "POST",
    body: formData,
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  const data: UserResponse = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Failed to reset token");
  }
  return data;
};

export const changePassword = async (
  token: string,
  payload: Record<string, any>
): Promise<UserResponse> => {
  const ENDPOINT = `${REST}${CONTROLLER}/changePassword`;
  const response = await fetch(ENDPOINT, {
    method: "PATCH",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  });
  const data: UserResponse = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Failed to change password");
  }
  return data;
};

export const loginWithFaceIdToken = async (
  token: string,
  idUsuario: string | number
): Promise<UserResponse> => {
  const ENDPOINT = `${REST}${CONTROLLER}/loginWithFaceIdToken`;
  const response = await fetch(ENDPOINT, {
    method: "POST",
    body: JSON.stringify({ idUsuario, faceIdToken: token }),
    headers: {
      "Content-Type": "application/json",
    },
  });
  const data: UserResponse = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Face ID login failed");
  }
  return data;
};

export const insertFaceIdToken = async (
  idUsuario: string | number,
  token: string
): Promise<UserResponse> => {
  const ENDPOINT = `${REST}${CONTROLLER}/setFaceIdToken`;
  const response = await fetch(ENDPOINT, {
    method: "POST",
    body: JSON.stringify({ idUsuario }),
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });
  const data: UserResponse = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Failed to set Face ID token");
  }
  return data;
};

export const resetFaceIdToken = async (
  idUsuario: string | number,
  token: string
): Promise<UserResponse> => {
  const ENDPOINT = `${REST}${CONTROLLER}/resetFaceIdToken`;
  const response = await fetch(ENDPOINT, {
    method: "POST",
    body: JSON.stringify({ idUsuario }),
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });
  const data: UserResponse = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Failed to reset Face ID token");
  }
  return data;
};

export const getPendingBills = async (
  policy: string,
  vtInstance: string,
  accessToken: string,
  internalToken: string
): Promise<any> => {
  const dataObj = {
    VisualTimeInstance: vtInstance,
    Policy: policy,
    PendingBills: 1,
  };

  const ENDPOINT = "https://integraciones.bminext.com/salesforce/getPendingBills";
  const response = await fetch(ENDPOINT, {
    method: "POST",
    body: JSON.stringify(dataObj),
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Bmi-Next-Internal-Token": internalToken,
      "Content-Type": "application/json",
    },
  });

  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Failed to fetch pending bills");
  }
  return data;
};

export const payBill = async (
  payload: Record<string, any>,
  accessToken: string,
  internalToken: string
): Promise<any> => {
  const ENDPOINT = "https://integraciones.bminext.com/salesforce/getPaymentLink";
  const response = await fetch(ENDPOINT, {
    method: "POST",
    body: JSON.stringify(payload),
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Bmi-Next-Internal-Token": internalToken,
      "Content-Type": "application/json",
    },
  });
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Failed to generate payment link");
  }
  return data;
};
