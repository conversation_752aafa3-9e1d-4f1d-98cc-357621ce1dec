import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { useNetworkState } from "@uidotdev/usehooks";

// Define la interfaz para el contexto
interface NetworkContextType {
  network: {
    online: boolean;
    downlink?: number | null;
    downlinkMax?: number | null;
    effectiveType?: string | null;
    rtt?: number | null;
    saveData?: boolean | null;
    type?: string | null;
  };
  touched: boolean;
  setTouched: (touched: boolean) => void;
}

// Crea el contexto con un valor inicial
const NetworkContext = createContext<NetworkContextType | undefined>(undefined);

// Define las props para el proveedor
interface NetworkProviderProps {
  children: ReactNode;
}

export const NetworkProvider: React.FC<NetworkProviderProps> = ({ children }) => {
  const network = useNetworkState();
  const [touched, setTouched] = useState(false);

  useEffect(() => {
    if (!network.online) {
      setTouched(false);
    }
  }, [network.online]);

  return (
    <NetworkContext.Provider value={{ network, touched, setTouched }}>
      {children}
    </NetworkContext.Provider>
  );
};

export const useNetwork = (): NetworkContextType => {
  const context = useContext(NetworkContext);
  if (context === undefined) {
    throw new Error("useNetwork must be used within a NetworkProvider");
  }
  return context;
};
