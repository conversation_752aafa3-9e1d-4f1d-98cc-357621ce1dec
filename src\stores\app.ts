
import { create } from "zustand";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState } from "../interfaces/app.interface";

// Define the store state type for better type safety


// Helper function to store data in AsyncStorage
const storeData = async (key: string, value: string) => {
  try {
    await AsyncStorage.setItem(key, value);
  } catch (error) {
    console.error(`Error storing ${key}:`, error);
  }
};

// Initialize store with data from AsyncStorage
const initializeStore = async (set: any) => {
  try {
    const cedula = await AsyncStorage.getItem('cedula');
    const idUsuario = await AsyncStorage.getItem('idUsuario');
    const internalToken = await AsyncStorage.getItem('internalToken');
    const accessToken = await AsyncStorage.getItem('accessToken');
    const faceIdToken = await AsyncStorage.getItem('faceIdToken');

    set({
      cedula: cedula || null,
      idUsuario: idUsuario || null,
      internalToken: internalToken || null,
      accessToken: accessToken || null,
      faceIdToken: faceIdToken || null,
    });
  } catch (error) {
    console.error('Error initializing store:', error);
  }
};

const useAppStore = create<AppState>((set) => {
  // Initialize store with AsyncStorage data
  initializeStore(set);

  return {
    cedula: null,
    nombre: null,
    accessToken: null,
    idUsuario: null,
    internalToken: null,
    pais: null,
    polizas: [],
    beneficiarios: {
      policyId: null,
      datos: [],
    },
    dependientes: {
      policyId: null,
      datos: [],
    },
    documentos: [],
    pagos: [],
    personalInformation: null,
    policyCards: [],
    tickets: [],
    reembolsos: {
      policyId: null,
      datos: [],
    },
    facturas: {
      policyId: null,
      impago: null,
      datos: [],
    },
    faceIdToken: null,

    setInfoLogin: (cedula, nombre, internalToken, idUsuario, pais, polizas) => {
      set({ cedula, nombre, internalToken, idUsuario, pais, polizas: polizas || [] });
      storeData('cedula', cedula);
      storeData('idUsuario', idUsuario);
      storeData('internalToken', internalToken);
    },

    setInfoRegistro: (cedula, nombre, internalToken, idUsuario, pais, accessToken) => {
      set({ cedula, nombre, internalToken, idUsuario, pais, accessToken });
      storeData('accessToken', accessToken);
      storeData('cedula', cedula);
      storeData('idUsuario', idUsuario);
      storeData('internalToken', internalToken);
    },

    setCedula: (cedula) => {
      set({ cedula });
      storeData('cedula', cedula);
    },

    setNombre: (nombre) => {
      set({ nombre });
    },

    setAccessToken: (accessToken) => {
      set({ accessToken });
      storeData('accessToken', accessToken);
    },

    setIdUsuario: (idUsuario) => {
      set({ idUsuario });
      storeData('idUsuario', idUsuario);
    },

    setInternalToken: (internalToken) => {
      set({ internalToken });
      storeData('internalToken', internalToken);
    },

    setFaceIdToken: (faceIdToken) => {
      set({ faceIdToken });
      storeData('faceIdToken', faceIdToken);
    },

    setPolizas: (polizas) => set({ polizas }),
    setBeneficiarios: (beneficiarios) => set({ beneficiarios }),
    setDependientes: (dependientes) => set({ dependientes }),
    setDocumentos: (documentos) => set({ documentos }),
    setPagos: (pagos) => set({ pagos }),
    setPersonalInformation: (personalInformation) => set({ personalInformation }),
    setPolicyCards: (policyCards) => set({ policyCards }),
    setTickets: (tickets) => set({ tickets }),
    setReembolsos: (reembolsos) => set({ reembolsos }),
    setFacturas: (facturas) => set({ facturas }),

    logout: async () => {
      set({
        cedula: null,
        nombre: null,
        accessToken: null,
        idUsuario: null,
        internalToken: null,
        polizas: [],
        beneficiarios: {
          policyId: null,
          datos: [],
        },
        dependientes: {
          policyId: null,
          datos: [],
        },
        documentos: [],
        pagos: [],
        personalInformation: null,
        policyCards: [],
        tickets: [],
        reembolsos: {
          policyId: null,
          datos: [],
        },
        facturas: {
          policyId: null,
          impago: null,
          datos: [],
        },
      });

      try {
        await AsyncStorage.removeItem('internalToken');
        await AsyncStorage.removeItem('accessToken');
        await AsyncStorage.removeItem('cedula');
        await AsyncStorage.removeItem('idUsuario');
        // Keep faceIdToken for biometric login
      } catch (error) {
        console.error('Error during logout:', error);
      }
    },
  };
});

export default useAppStore;
