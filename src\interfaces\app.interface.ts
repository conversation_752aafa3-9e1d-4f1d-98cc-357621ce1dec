export interface AppState {
  cedula: string | null;
  nombre: string | null;
  accessToken: string | null;
  idUsuario: string | null;
  internalToken: string | null;
  pais: string | null;
  polizas: any[];
  beneficiarios: {
    policyId: string | null;
    datos: any[];
  };
  dependientes: {
    policyId: string | null;
    datos: any[];
  };
  documentos: any[];
  pagos: any[];
  personalInformation: any | null;
  policyCards: any[];
  tickets: any[];
  reembolsos: {
    policyId: string | null;
    datos: any[];
  };
  facturas: {
    policyId: string | null;
    impago: any | null;
    datos: any[];
  };
  faceIdToken: string | null;

  // Methods
  setInfoLogin: (cedula: string, nombre: string, internalToken: string, idUsuario: string, pais: string, polizas: any[] | null) => void;
  setInfoRegistro: (cedula: string, nombre: string, internalToken: string, idUsuario: string, pais: string, accessToken: string) => void;
  setCedula: (cedula: string) => void;
  setNombre: (nombre: string) => void;
  setAccessToken: (accessToken: string) => void;
  setIdUsuario: (idUsuario: string) => void;
  setInternalToken: (internalToken: string) => void;
  setFaceIdToken: (faceIdToken: string) => void;
  setPolizas: (polizas: any[]) => void;
  setBeneficiarios: (beneficiarios: any) => void;
  setDependientes: (dependientes: any) => void;
  setDocumentos: (documentos: any[]) => void;
  setPagos: (pagos: any[]) => void;
  setPersonalInformation: (personalInformation: any) => void;
  setPolicyCards: (policyCards: any[]) => void;
  setTickets: (tickets: any[]) => void;
  setReembolsos: (reembolsos: any) => void;
  setFacturas: (facturas: any) => void;
  logout: () => void;
}
