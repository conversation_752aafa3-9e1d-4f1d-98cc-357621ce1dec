import { create } from "zustand";
import { ToastState } from "../interfaces/toast.interface";


const useToastStore = create<ToastState>((set) => ({
  showToast: false,
  message: "",
  timeoutId: null,
  type: "success",
  show: ({ msg, duration = 3000, type = "success" }) => {
    set((state) => {
      // Limpiar cualquier timeout anterior
      if (state.timeoutId) {
        clearTimeout(state.timeoutId);
      }

      // Mostrar el toast y configurar un nuevo timeout
      const id = setTimeout(
        () => set({ showToast: false, message: "", timeoutId: null }),
        duration
      );

      return {
        showToast: true,
        message: msg,
        timeoutId: id,
        type
      };
    });
  },
  hide: () => set({
    showToast: false,
    message: "",
    timeoutId: null
  }),
}));

export default useToastStore;
