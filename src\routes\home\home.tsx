
import { useState } from "react";
import {
  SafeAreaView,
  View,
  Text,
  ActivityIndicator,
  TouchableOpacity,
  TextInput,
  Button
} from 'react-native';
import { login } from "../../services/users.service.ts";
import useAppStore from "../../stores/app";
import useToastStore from "../../stores/toast";
import { handleAccessTokenFirstLogin } from "../../hooks/salesforceAuth.hook";
import ForgottenPassword from "../../components/forgottenPassword";
import { useNetwork } from "../../../network-provider.tsx";
import Offline from "../../components/offline";
import { styles } from './home-style.ts';
import BmiNextLogo from '../../assets/bminext-logo.svg';



function Home() {
  const { network, touched } = useNetwork();
  //const navigation = useNavigation();
  const [openLogin, setOpenLogin] = useState(false);
  const [openForgotPassword, setOpenForgotPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const [loading, setLoading] = useState(false);
  const { setInfoLogin, accessToken } = useAppStore();
  const [valid, setValid] = useState(false);
  const { show } = useToastStore();
  const [faceIdToken, setFaceIdToken] = useState(
    localStorage.getItem("faceIdToken")
  );

  const handleChangeEmail = (value:any) => {
    setEmail(value);
    setValid(value && password);
  };

  const handleChangePassword = (value:any) => {
    setPassword(value);
    setValid(email && value);
  };



  const handleLogin = async () => {
    if (!email || !password) {
      show({ msg: "Por favor, ingresa tu correo y contraseña", type: "error" });
      return;
    }
    try {
      setLoading(true);
      const { usuario, polizas } = await login(email, password);
      setInfoLogin(
        usuario.nif,
        usuario.nombre,
        usuario.token,
        usuario.id,
        usuario.residenciaFiscal,
        polizas || null
      );
      if (polizas && polizas.length > 0) {
        if (!accessToken) {
          await handleAccessTokenFirstLogin(usuario.nif);
        }
        //navigation.navigate('App');
      } else {
        console.log("antes del navigate");
        //navigation.navigate('App');
      }
    } catch (error:any) {
      show({ msg: error.message, type: "error" });
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleBiometricLogin = async () => {
    // Implement biometric login logic here
  };

  return (
    <SafeAreaView style={styles.main}>
      {/* Offline Component */}
      {!network?.online && !touched && <Offline />}

      {/* Logo & Title */}
      <View style={styles.imgTitleContainer}>
        <BmiNextLogo width={129.6} height={90} style={styles.logo} />
        <Text style={styles.title}>Asegurando familias en todo el mundo</Text>
      </View>

      {/* Conditional Buttons */}
      {faceIdToken ? (
        <View style={styles.buttonsContainer}>
          {loading ? (
            <ActivityIndicator size="large" color="#2054A5" />
          ) : (
            <>
              <View style={styles.biometricButton}>
                <Button  onPress={handleBiometricLogin}  title="Acceso biométrico" />
              </View>

              <TouchableOpacity onPress={() => setFaceIdToken(null)}>
                <Text style={styles.passwordLink}>Accede con contraseña</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      ) : (
        <View style={styles.buttonsContainer}>
          <Button  onPress={() => setOpenLogin(true)} title="Ingresa" />
            Ingresa

         {/* <Button  onPress={() => navigation.navigate('Registro')}>
            Regístrate
          </Button>*/}
        </View>
      )}

      {/* Drawer/Login Form */}
      {openLogin && (
        <View style={styles.drawer}>
          <Text style={styles.drawerTitle}>Hola de nuevo</Text>
          <View style={styles.loginForm}>
            <View style={styles.formGroup}>
              <TextInput
                style={styles.input}
                placeholder="Correo electrónico"
                keyboardType="email-address"
                value={email}
                onChangeText={handleChangeEmail}
              />
            </View>
            <View style={styles.formGroup}>
              <TextInput
                style={styles.input}
                placeholder="Contraseña"
                secureTextEntry
                value={password}
                onChangeText={handleChangePassword}
              />
            </View>
            {loading ? (
              <ActivityIndicator size="small" color="#2054A5" />
            ) : (
              <Button  disabled={!valid} onPress={handleLogin} title="Ingresar" />


            )}
            <TouchableOpacity onPress={() => {
              setOpenForgotPassword(true);
              setOpenLogin(false);
            }}>
              <Text style={styles.forgotPassword}>¿Olvidaste tu contraseña?</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Forgot Password Modal */}
      <ForgottenPassword open={openForgotPassword} setOpen={setOpenForgotPassword} />
    </SafeAreaView>
  );
}


export default Home;
