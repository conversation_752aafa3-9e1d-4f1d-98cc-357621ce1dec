import AsyncStorage from '@react-native-async-storage/async-storage';
import { getAccessToken } from "../services/salesforceauth.service";
import useAppStore from "../stores/app";

export const EXPIRATION_DAYS_TIME = 30;

export const isTokenExpired = async (): Promise<boolean> => {
  try {
    const expirationDate = await AsyncStorage.getItem("expirationDate");
    if (!expirationDate) return true;

    console.log("Comparando fechas:", new Date(), new Date(expirationDate));
    if (new Date() > new Date(expirationDate)) return true;

    return false;
  } catch (error) {
    console.error("Error verificando expiración del token:", error);
    return true; // Si hay un error al verificar, consideramos el token expirado
  }
};

export const handleAccessToken = async (): Promise<[string, string, string] | undefined> => {
  try {
    const cedula = await AsyncStorage.getItem("cedula");
    const idUsuario = await AsyncStorage.getItem("idUsuario");

    if (!cedula || !idUsuario) {
      console.log("No hay cédula o idUsuario, no se puede obtener token ni volcar datos aún");
      return undefined;
    }

    const { setAccessToken, setCedula, setIdUsuario } = useAppStore.getState();
    setCedula(cedula);
    setIdUsuario(idUsuario);

    const tokenLS = await AsyncStorage.getItem("accessToken");
    const isExpired = await isTokenExpired();

    if (!tokenLS || isExpired) {
      console.log("No hay token o está expirado, intentamos obtener uno nuevo");
      const { access_token, updated } = await getAccessToken(cedula);
      setAccessToken(access_token);

      const expirationDate = new Date(
        new Date(updated).getTime() + EXPIRATION_DAYS_TIME * 24 * 60 * 60 * 1000
      );

      await AsyncStorage.setItem("expirationDate", expirationDate.toISOString());
      return [cedula, access_token, idUsuario];
    } else {
      console.log("Token válido");
      setAccessToken(tokenLS);
      return [cedula, tokenLS, idUsuario];
    }
  } catch (error) {
    console.error("Error en handleAccessToken:", error);
    return undefined;
  }
};

export const handleAccessTokenFirstLogin = async (cedula: string): Promise<void> => {
  try {
    const { setAccessToken } = useAppStore.getState();
    const { access_token, updated } = await getAccessToken(cedula);
    setAccessToken(access_token);

    const expirationDate = new Date(
      new Date(updated).getTime() + EXPIRATION_DAYS_TIME * 24 * 60 * 60 * 1000
    );

    await AsyncStorage.setItem("expirationDate", expirationDate.toISOString());
  } catch (error) {
    console.error("Error en handleAccessTokenFirstLogin:", error);
    throw error; // Re-lanzamos el error para que pueda ser manejado por el llamador
  }
};
