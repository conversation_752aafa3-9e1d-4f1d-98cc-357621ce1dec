

import React from 'react';

import Home from './src/routes/home/<USER>';
import { StatusBar, useColorScheme } from 'react-native';
import { NetworkProvider } from './network-provider';



function App() {
  const isDarkMode = useColorScheme() === 'dark';

  return (
    <NetworkProvider>
        <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
        <Home/>
    </NetworkProvider>
  );
}


export default App;
